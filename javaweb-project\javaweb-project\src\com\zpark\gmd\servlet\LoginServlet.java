package com.zpark.gmd.servlet;

import com.zpark.gmd.beans.Employee;
import com.zpark.gmd.utils.JDBCUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@WebServlet("/login")
public class LoginServlet extends HttpServlet {
    @Override
    protected void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        //中文乱码解决
        req.setCharacterEncoding("utf-8");
        resp.setContentType("text/html;charset=utf-8");
        //获取登录页面提交的参数
        String username = req.getParameter("username");
        String password = req.getParameter("password");
        //晓燕体骄傲的参数
        System.out.println(username+"--------"+password);
        //获取响应对象的字符输出流
        PrintWriter writer = resp.getWriter();
        //虎丘数据库连接对象
        Connection conn = JDBCUtil.getConn();
        PreparedStatement pst = null;
        ResultSet rs= null;
        //定义SQL语句
        String sql ="select * from employee where name =? and password=?";
        try {
            pst = conn.prepareStatement(sql);
            pst.setString(1,username);
            pst.setString(2,password);
            rs = pst.executeQuery();
            boolean isLogin = rs.next();
            if (isLogin){
                //String loginName = rs.getString("name");
                Employee employee = new Employee();
                employee.setId(rs.getInt("id"));
                employee.setName(rs.getString("name"));
                employee.setPassword(rs.getString("password"));
                employee.setGender(rs.getString("gender"));
                employee.setSalary(rs.getInt("salary"));
                employee.setJoinDate(rs.getDate("join_date"));
                employee.setDepId(rs.getInt("dept_id"));
                //session对象绑定登录名称
                req.getSession().setAttribute("employee",employee);
                //重定向到index.jsp
                resp.sendRedirect(req.getContextPath()+"/index.jsp");

            }else{
                //转发信息login.jsp
                //设置密码错误提示
                req.setAttribute("msg","用户名密码错误");
                //转发信息到login.jsp页面
                req.getRequestDispatcher("login.jsp").forward(req,resp);
                resp.sendRedirect(req.getContextPath()+"/login.jsp");

            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }finally {
            JDBCUtil.closeAll(conn,pst,rs);;
            writer.close();
        }
    }
}
