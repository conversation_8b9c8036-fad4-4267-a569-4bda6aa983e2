<component name="ArtifactManager">
  <artifact type="exploded-war" name="javaweb-project:Web exploded">
    <output-path>$PROJECT_DIR$/out/artifacts/javaweb_project_Web_exploded</output-path>
    <root id="root">
      <element id="javaee-facet-resources" facet="javaweb-project/web/Web" />
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="javaweb-project" />
        </element>
      </element>
    </root>
  </artifact>
</component>