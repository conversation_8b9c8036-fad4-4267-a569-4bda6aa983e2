package com.zpark.gmd.servlet;

import com.zpark.gmd.beans.Employee;
import com.zpark.gmd.utils.JDBCUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.rmi.RemoteException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;


@WebServlet("/emp/*")
public class EmployeeServlet extends HttpServlet {
    @Override
    public void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        //中文乱码解决
        req.setCharacterEncoding("UTF-8");
        resp.setContentType("text/html;charset=UTF-8");
        //申明数据库资源对象
        Connection conn=null;
        PreparedStatement pstmt=null;
        ResultSet rs= null;

        //获取请求资源路径
        String requestURI = req.getRequestURI();
        System.out.println( requestURI );
        //截取子字符串获取最后一个\的下标
        int beginIndex = requestURI.lastIndexOf("/");
        //截取子字符串
        String substring = requestURI.substring(beginIndex);
        //根据资源请求路径区分不同的功能！
        if(substring.equals("/findAllEmps")){
            Employee employee =null;
            ArrayList<Employee> list = new ArrayList<>();
            //获取数据库连接对象
            conn = JDBCUtil.getConn();
            //定义SQL语句
            String sql="select * from employee";
            try {
                //获取对象
                pstmt=conn.prepareStatement(sql);
                //返回结果集
                rs= pstmt.executeQuery();
                while(rs.next()) {
                    employee = new Employee();
                    employee.setId(rs.getInt("id"));
                    employee.setName(rs.getString("name"));
                    employee.setGender(rs.getString("gender"));
                    employee.setSalary(rs.getInt("salary"));
                    employee.setJoinDate(rs.getDate("join_date"));
                    employee.setPassword(rs.getString("password"));
                    employee.setDepId(rs.getInt("dept_id"));
                    list.add(employee);
                }
                //System.out.println(list);
                req.setAttribute("list",list);
                req.getRequestDispatcher("/list.jsp").forward(req,resp);

            } catch (SQLException e) {
                throw new RuntimeException(e);
            }finally {
                JDBCUtil.closeAll(conn,pstmt,rs);
            }
        }else if(substring.equals("/delEmpById")){
            //根据ID删除员工信息
            System.out.println("执行删除操作");
            String idStr = req.getParameter("id");
            int id = Integer.parseInt(idStr);
            System.out.println("执行删除,并且提交的是"+id);
            //获取数据库连接对象
            conn = JDBCUtil.getConn();
            String sql="delete from employee where id=?";
            try {
                //获取执行sql对象
                pstmt = conn.prepareStatement(sql);
                //替换占位符
                pstmt.setInt(1,id);
                //执行sql语句并返回结果
                pstmt.executeUpdate();
                //重定向
                resp.sendRedirect(req.getContextPath()+"/emp/findAllEmps");
            } catch (SQLException e) {
                throw new RuntimeException(e);

            }finally {
                JDBCUtil.closeAll(conn,pstmt,null);

            }


        }
            //获取执行SQL对象

        }

        //增加
        //删除
        //修改
        //查找

}
