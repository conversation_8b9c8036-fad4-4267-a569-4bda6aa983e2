# javaweb-project项目在Linux环境下使用Tomcat部署详细步骤

本文档详细介绍如何将`javaweb-project` Maven项目部署到Linux服务器，使用Tomcat启动并访问项目。部署过程中需要用到MySQL数据库、JDK环境和Apache Tomcat服务器。

---

## 一、准备工作

- 一台具有root或sudo权限的Linux服务器
- 具备网络访问权限以下载所需软件
- 已经打包好的`javaweb-project`项目WAR文件（或源码可自行打包）

---

## 二、安装JDK环境

1. 更新软件包索引：
   ```bash
   sudo apt update
   ```
2. 安装OpenJDK 11（可根据需要选择版本）：
   ```bash
   sudo apt install openjdk-11-jdk -y
   ```
3. 验证JDK安装是否成功：
   ```bash
   java -version
   ```

---

## 三、安装MySQL数据库

1. 安装MySQL服务器：
   ```bash
   sudo apt install mysql-server -y
   ```
2. 运行安全配置脚本，设置root密码并进行安全加固：
   ```bash
   sudo mysql_secure_installation
   ```
3. 登录MySQL：
   ```bash
   sudo mysql -u root -p
   ```
4. 创建项目数据库：
   ```sql
   CREATE DATABASE javaweb_db CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
   ```
5. 创建数据库用户并授权：
   ```sql
   CREATE USER 'javaweb_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON javaweb_db.* TO 'javaweb_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

---

## 四、安装Apache Tomcat

1. 下载Tomcat（以9.0.73版本为例）：
   ```bash
   wget https://downloads.apache.org/tomcat/tomcat-9/v9.0.73/bin/apache-tomcat-9.0.73.tar.gz
   ```
2. 解压下载包：
   ```bash
   tar -xzf apache-tomcat-9.0.73.tar.gz
   ```
3. 移动Tomcat目录到`/opt`：
   ```bash
   sudo mv apache-tomcat-9.0.73 /opt/tomcat
   ```
4. 赋予执行权限：
   ```bash
   sudo chmod +x /opt/tomcat/bin/*.sh
   ```

---

## 五、构建项目WAR包

1. 进入项目目录（本地或服务器）：
   ```bash
   cd /path/to/javaweb-project
   ```
2. 使用Maven构建项目：
   ```bash
   mvn clean package
   ```
3. 构建成功后，WAR包位于`target`目录下：
   ```
   target/javaweb-project.war
   ```

---

## 六、部署WAR包到Tomcat

1. 将WAR包复制到Tomcat的`webapps`目录：
   ```bash
   sudo cp target/javaweb-project.war /opt/tomcat/webapps/
   ```
2. Tomcat启动时会自动解压并部署该WAR包。

---

## 七、配置数据库连接

1. 修改项目中的数据库配置文件（如`druid.properties`或其他配置文件），填写MySQL数据库连接信息：
   ```
   jdbc.url=**********************************************************************************************************
   jdbc.username=javaweb_user
   jdbc.password=your_password
   ```
2. 确保配置文件被正确打包或放置在项目的类路径下。

---

## 八、启动Tomcat服务器

1. 启动Tomcat：
   ```bash
   /opt/tomcat/bin/startup.sh
   ```
2. 查看Tomcat日志确认启动成功：
   ```bash
   tail -f /opt/tomcat/logs/catalina.out
   ```

---

## 九、访问项目

1. 打开浏览器，访问：
   ```
   http://服务器IP:8080/javaweb-project
   ```
2. 如果页面正常显示，说明部署成功。

---

## 十、停止Tomcat服务器（如需）

```bash
/opt/tomcat/bin/shutdown.sh
```

---

## 附录

- 若需配置防火墙开放8080端口，请根据Linux发行版使用`ufw`或`firewalld`进行配置。
- 确保MySQL服务和Tomcat服务均已启动。

---

以上即为`javaweb-project`项目在Linux环境下使用Tomcat部署的详细步骤。
