<component name="ArtifactManager">
  <artifact type="war" name="javaweb-project">
    <output-path>$PROJECT_DIR$/out/artifacts/javaweb_project</output-path>
    <root id="archive" name="javaweb-project.war">
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="javaweb-project" />
        </element>
        <element id="directory" name="lib">
          <element id="library" level="project" name="druid-1.2.8" />
          <element id="library" level="project" name="jstl-1.2" />
          <element id="library" level="project" name="standard-1.1.2" />
        </element>
      </element>
      <element id="javaee-facet-resources" facet="javaweb-project/web/Web" />
    </root>
  </artifact>
</component>