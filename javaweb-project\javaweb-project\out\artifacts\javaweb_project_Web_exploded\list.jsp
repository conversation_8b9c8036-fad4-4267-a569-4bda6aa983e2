<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="com.zpark.gmd.beans.Employee" %><%--
  Created by IntelliJ IDEA.
  User: jiang
  Date: 2025/6/5
  Time: 21:59
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.zpark.gmd.beans.Employee" %>
<html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="<PERSON>, <PERSON>, and Bootstrap contributors">
    <meta name="generator" content="Hugo 0.88.1">
    <title>员工的列表页面</title>
    <!-- Bootstrap core CSS -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body>

<!--这是topbar插入的地方-->
<jsp:include page="commons/topbar.jsp"></jsp:include>

<div class="container-fluid">
    <div class="row">

        <!--sidebar插入的地方-->
        <jsp:include page="commons/sidebar.jsp"></jsp:include>
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <h2>查询所有员工信息</h2>
            <div class="table-responsive">

                <table class="table table-striped table-sm">
                    <thead>
                    <tr>
                        <th scope="col">员工编号</th>
                        <th scope="col">员工姓名</th>
                        <th scope="col">员工性别</th>
                        <th scope="col">员工薪资</th>
                        <th scope="col">员工时间</th>
                        <th scope="col">部门编号</th>
                        <th scope="col">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${list}" var="employee">

                        <tr>
                            <td>${employee.id}</td>
                            <td>${employee.name}</td>
                            <td>${employee.gender=="0"?"女":"男"}</td>
                            <td>${employee.salary}</td>
                            <td>${employee.joinDate}</td>
                            <td>${employee.depId}</td>


                            <td>
                                <button type="button" class="btn-success">修改</button>
                                    <a href="${pageContext.request.getContextPath()}/emp/delEmpById?id=${employee.id}">
                                        <button type="button" class="btn-danger">删除</button>
                                    </a>
                            </td>
                        </tr>
                    </c:forEach>

                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>
<script src="assets/js/feather.min.js"></script>
<script src="assets/js/dashboard.js"></script>
</body>
</html>

