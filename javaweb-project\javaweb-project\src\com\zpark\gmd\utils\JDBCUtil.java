package com.zpark.gmd.utils;

import com.alibaba.druid.pool.DruidDataSourceFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;
import java.util.Properties;

public class JDBCUtil {
    //定义数据源对象
    private static DataSource ds = null;
    //获取数据库的数据对象
    static{
        //创建Properties配置文件对象
        Properties properties = new Properties();
        //加载项目中的配置文件
        try {
            properties.load(JDBCUtil.class.getClassLoader().getResourceAsStream("druid.properties"));
            ds= DruidDataSourceFactory.createDataSource(properties);
        } catch (Exception e) {
           throw new RuntimeException(e);
        }
    }

    public static Connection getConn(){
        try {
            return ds.getConnection();
        } catch (SQLException e) {
            return null;
        }
    }


    //关闭资源
    public static void closeAll(Connection conn, PreparedStatement pstmt, ResultSet rs){
        try{
            if(Objects.nonNull(rs)){
                rs.close();
            }
            if(Objects.nonNull(pstmt)){
                pstmt.close();
            }
            if(Objects.nonNull(conn)){
                conn.close();
            }
        }catch ( SQLException e){
            throw new RuntimeException(e);
        }

    }

}
