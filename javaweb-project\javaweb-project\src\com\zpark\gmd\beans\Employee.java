package com.zpark.gmd.beans;

import java.sql.Date;

public class Employee {
    private Integer id;
    private String  name;
    private String gender;
    private Integer salary;
    private Date joinDate;
    private Integer depId;
    private String password;

    public Employee() {
    }

    public Employee(Integer id, String name, String gender, Integer salary, Date joinDate, Integer depId, String password) {
        this.id = id;
        this.name = name;
        this.gender = gender;
        this.salary = salary;
        this.joinDate = joinDate;
        this.depId = depId;
        this.password = password;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getSalary() {
        return salary;
    }

    public void setSalary(Integer salary) {
        this.salary = salary;
    }

    public Date getJoinDate() {
        return joinDate;
    }

    public void setJoinDate(Date joinDate) {
        this.joinDate = joinDate;
    }

    public Integer getDepId() {
        return depId;
    }

    public void setDepId(Integer depId) {
        this.depId = depId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "Employee{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", salary=" + salary +
                ", joinDate=" + joinDate +
                ", depId=" + depId +
                ", password='" + password + '\'' +
                '}';
    }
}
